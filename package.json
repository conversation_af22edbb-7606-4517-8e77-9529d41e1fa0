{"name": "microchip-ai-chatbot", "displayName": "Microchip AI Chatbot", "description": "AI-powered chatbot for Microchip development assistance", "version": "1.0.0", "publisher": "microchip-ai", "repository": {"type": "git", "url": "https://github.com/microchip-ai/vscode-chatbot.git"}, "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "keywords": ["microchip", "ai", "chatbot", "assistant", "development"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "microchipAI", "title": "Microchip AI", "icon": "$(robot)"}]}, "views": {"microchipAI": [{"id": "microchipAIChatbot", "name": "AI Chatbot", "when": "true", "icon": "$(comment-discussion)"}]}, "commands": [{"command": "microchipAIChatbot.openChat", "title": "Open Microchip AI Chat", "icon": "$(comment-discussion)"}, {"command": "microchipAIChatbot.resetApiKey", "title": "Reset API Key", "icon": "$(key)"}], "menus": {"view/title": [{"command": "microchipAIChatbot.openChat", "when": "view == microchipAIChatbot", "group": "navigation"}, {"command": "microchipAIChatbot.resetApiKey", "when": "view == microchipAIChatbot", "group": "navigation"}]}}, "scripts": {"vscode:prepublish": "npm run compile && npm run build:webview", "compile": "tsc -p ./tsconfig.extension.json", "watch": "tsc -watch -p ./tsconfig.extension.json", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "build:webview": "vite build --config vite.webview.config.ts", "dev:webview": "vite --config vite.webview.config.ts", "server": "node server/index.cjs", "package": "npx @vscode/vsce package", "dev:full": "concurrently \"npm run server\" \"npm run dev:webview\"", "build:all": "npm run compile && npm run build:webview"}, "dependencies": {"@browserbasehq/stagehand": "^1.14.0", "@ibm-cloud/watsonx-ai": "^1.6.8", "@langchain/anthropic": "^0.3.23", "@langchain/community": "^0.3.47", "@langchain/core": "^0.3.61", "@langchain/google-genai": "^0.2.13", "@langchain/openai": "^0.5.15", "@playwright/test": "^1.53.1", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "cheerio": "^1.1.0", "cors": "^2.8.5", "deepmerge": "^4.3.1", "dotenv": "^16.5.0", "express": "^5.1.0", "ibm-cloud-sdk-core": "^5.4.0", "langchain": "^0.3.29", "openai": "^4.67.3", "react": "^19.1.0", "react-dom": "^19.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^20.19.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/vscode": "^1.74.0", "@vitejs/plugin-react": "^4.5.2", "@vscode/test-electron": "^2.3.4", "@vscode/vsce": "^3.6.0", "concurrently": "^9.2.0", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "vsce": "^2.15.0"}}